import mongoose from "mongoose";

// Activity attachment schema for reference materials
const activityAttachmentSchema = new mongoose.Schema({
  name: { type: String, required: true },
  url: { type: String, required: true },
  size: { type: Number },
  type: { type: String },
  fileName: { type: String },
}, { _id: false });

const activitySchema = new mongoose.Schema(
  {
    // Core activity fields
    task: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "Task", 
      required: true,
      index: true // Index for faster task-based queries
    },
    user: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User", 
      required: true 
    },
    
    // Activity type and content
    type: { 
      type: String, 
      required: true,
      enum: [
        // Manual activities (user-generated)
        "COMMENT",
        "ATTACHMENT_ADDED",
        
        // System activities (auto-generated)
        "TASK_CREATED",
        "STATUS_CHANGED",
        "PRIORITY_CHANGED",
        "DUE_DATE_CHANGED",
        "ASSIGNMENT_CHANGED",
        "CHECKLIST_UPDATED",
        "TASK_UPDATED",
        "TASK_DELETED"
      ]
    },
    
    // Activity content
    content: {
      // For comments
      text: { type: String },
      
      // For attachments (reference materials)
      attachments: [activityAttachmentSchema],
      
      // For system activities - track changes
      changes: {
        field: { type: String }, // e.g., "status", "priority", "dueDate"
        oldValue: { type: mongoose.Schema.Types.Mixed },
        newValue: { type: mongoose.Schema.Types.Mixed }
      },
      
      // Additional metadata
      metadata: { type: mongoose.Schema.Types.Mixed, default: {} }
    },
    
    // Activity status
    isVisible: { type: Boolean, default: true },
    isEdited: { type: Boolean, default: false },
    editedAt: { type: Date },
    
    // Collaboration metrics (for ranking algorithm)
    metrics: {
      responseTime: { type: Number }, // Time taken to respond (in minutes)
      engagementScore: { type: Number, default: 1 }, // Base engagement score
      helpfulnessRating: { type: Number, default: 0 }, // User ratings (future feature)
    }
  },
  { 
    timestamps: true,
    // Add indexes for better query performance
    index: [
      { task: 1, createdAt: -1 }, // For timeline queries
      { user: 1, createdAt: -1 }, // For user activity history
      { type: 1, createdAt: -1 }  // For activity type filtering
    ]
  }
);

// Pre-save middleware to calculate response time for collaboration ranking
activitySchema.pre('save', async function(next) {
  if (this.isNew && this.type === 'COMMENT') {
    try {
      // Find the last activity in this task by a different user
      const lastActivity = await this.constructor.findOne({
        task: this.task,
        user: { $ne: this.user },
        type: { $in: ['COMMENT', 'TASK_CREATED', 'ASSIGNMENT_CHANGED'] }
      }).sort({ createdAt: -1 });
      
      if (lastActivity) {
        // Calculate response time in minutes
        const responseTimeMs = Date.now() - lastActivity.createdAt.getTime();
        this.metrics.responseTime = Math.round(responseTimeMs / (1000 * 60));
        
        // Adjust engagement score based on response time
        if (this.metrics.responseTime <= 60) { // Within 1 hour
          this.metrics.engagementScore = 3;
        } else if (this.metrics.responseTime <= 1440) { // Within 24 hours
          this.metrics.engagementScore = 2;
        } else {
          this.metrics.engagementScore = 1;
        }
      }
    } catch (error) {
      console.error('Error calculating response time:', error);
    }
  }
  next();
});

// Static method to create system activity
activitySchema.statics.createSystemActivity = async function(taskId, userId, type, changes) {
  const activity = new this({
    task: taskId,
    user: userId,
    type: type,
    content: {
      changes: changes
    }
  });
  
  return await activity.save();
};

// Static method to create comment activity
activitySchema.statics.createComment = async function(taskId, userId, text) {
  const activity = new this({
    task: taskId,
    user: userId,
    type: 'COMMENT',
    content: {
      text: text
    }
  });
  
  return await activity.save();
};

// Static method to create attachment activity
activitySchema.statics.createAttachmentActivity = async function(taskId, userId, attachments) {
  const activity = new this({
    task: taskId,
    user: userId,
    type: 'ATTACHMENT_ADDED',
    content: {
      attachments: attachments
    }
  });
  
  return await activity.save();
};

// Instance method to get formatted activity description
activitySchema.methods.getDescription = function() {
  switch (this.type) {
    case 'COMMENT':
      return this.content.text;
    case 'ATTACHMENT_ADDED':
      const count = this.content.attachments?.length || 1;
      return `Added ${count} attachment${count > 1 ? 's' : ''}`;
    case 'STATUS_CHANGED':
      return `Changed status from "${this.content.changes.oldValue}" to "${this.content.changes.newValue}"`;
    case 'PRIORITY_CHANGED':
      return `Changed priority from "${this.content.changes.oldValue}" to "${this.content.changes.newValue}"`;
    case 'DUE_DATE_CHANGED':
      return `Changed due date from "${this.content.changes.oldValue}" to "${this.content.changes.newValue}"`;
    case 'ASSIGNMENT_CHANGED':
      return `Updated task assignments`;
    case 'CHECKLIST_UPDATED':
      return `Updated task checklist`;
    case 'TASK_CREATED':
      return `Created this task`;
    case 'TASK_UPDATED':
      return `Updated task details`;
    default:
      return `Performed ${this.type.toLowerCase().replace('_', ' ')}`;
  }
};

export default mongoose.model("Activity", activitySchema);
