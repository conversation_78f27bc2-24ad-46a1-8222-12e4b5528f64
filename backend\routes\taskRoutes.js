import express from "express";
import { protect } from "../middlewares/authMiddleware.js";
import {
  getDashboardData,
  getUserDashboardData,
  getTaskById,
  getTasks,
  createTask,
  updateTask,
  deleteTask,
  updateTaskStatus,
  updateTaskChecklist,
  syncTaskReferences,
} from "../controllers/taskController.js";
import {
  updateSubtaskStatus,
  assignSubtask,
  getSubtaskAnalytics
} from "../controllers/subtaskController.js";
import {
  getTaskActivities,
  addComment,
  addAttachment,
  updateActivity,
  deleteActivity,
  getActivityStats
} from "../controllers/activityController.js";

const router = express.Router();

// Task Management Routes
router.get("/dashboard-data", protect, getDashboardData);
router.get("/user-dashboard-data", protect, getUserDashboardData);
router.get("/", protect, getTasks); // Get all tasks (User: assigned tasks)
router.get("/:id", protect, getTaskById); // Get task by ID
router.post("/", protect, createTask); // Create a task (Previously admin only, now any protected user)
router.put("/:id", protect, updateTask); // Update task details
router.delete("/:id", protect, deleteTask); // Delete a task (Previously admin only, now any protected user)
router.put("/:id/status", protect, updateTaskStatus); // Update task status
router.put("/:id/todo", protect, updateTaskChecklist); // Update task checklist

// Enhanced Subtask Management Routes
router.put("/:taskId/subtasks/:subtaskIndex/status", protect, updateSubtaskStatus); // Update subtask status with collaboration tracking
router.put("/:taskId/subtasks/:subtaskIndex/assign", protect, assignSubtask); // Assign subtask to user
router.get("/:taskId/subtasks/analytics", protect, getSubtaskAnalytics); // Get subtask collaboration analytics

// Activity Management Routes
router.get("/:taskId/activities", protect, getTaskActivities); // Get all activities for a task
router.post("/:taskId/activities/comment", protect, addComment); // Add comment to task
router.post("/:taskId/activities/attachment", protect, addAttachment); // Add attachment to task activity
router.put("/:taskId/activities/:activityId", protect, updateActivity); // Update activity (comments only)
router.delete("/:taskId/activities/:activityId", protect, deleteActivity); // Delete activity
router.get("/:taskId/activities/stats", protect, getActivityStats); // Get activity stats for collaboration ranking

// Utility routes
router.post("/sync-user-references", protect, syncTaskReferences); // Sync existing tasks with user references

export default router;
