import Activity from "../models/Activity.js";
import Task from "../models/Task.js";
import { NotificationService } from "../services/notificationService.js";

// @desc    Get all activities for a specific task
// @route   GET /api/tasks/:taskId/activities
// @access  Private
const getTaskActivities = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { page = 1, limit = 50, type } = req.query;

    // Verify user has access to this task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    // Check if user is involved in this task (creator or assignee)
    const isInvolved = task.createdBy.toString() === req.user._id.toString() ||
                      task.assignedTo.some(userId => userId.toString() === req.user._id.toString());
    
    if (!isInvolved) {
      return res.status(403).json({ message: "Access denied to this task" });
    }

    // Build query
    const query = { task: taskId, isVisible: true };
    if (type) {
      query.type = type;
    }

    // Get activities with pagination
    const activities = await Activity.find(query)
      .populate('user', 'name email profileImageUrl')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    // Get total count for pagination
    const total = await Activity.countDocuments(query);

    res.json({
      activities,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error("Error fetching task activities:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Add a comment to a task
// @route   POST /api/tasks/:taskId/activities/comment
// @access  Private
const addComment = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { text } = req.body;

    if (!text || text.trim().length === 0) {
      return res.status(400).json({ message: "Comment text is required" });
    }

    // Verify user has access to this task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    const isInvolved = task.createdBy.toString() === req.user._id.toString() ||
                      task.assignedTo.some(userId => userId.toString() === req.user._id.toString());
    
    if (!isInvolved) {
      return res.status(403).json({ message: "Access denied to this task" });
    }

    // Create comment activity
    const activity = await Activity.createComment(taskId, req.user._id, text.trim());
    
    // Populate user data
    await activity.populate('user', 'name email profileImageUrl');

    // Create notifications for other task participants
    try {
      const participants = [task.createdBy, ...task.assignedTo]
        .filter(userId => userId.toString() !== req.user._id.toString());
      
      for (const participantId of participants) {
        await NotificationService.createTaskCommentNotification(task, req.user, participantId);
      }
    } catch (notificationError) {
      console.error("Error creating comment notifications:", notificationError);
    }

    res.status(201).json({
      message: "Comment added successfully",
      activity
    });
  } catch (error) {
    console.error("Error adding comment:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Add attachment(s) to a task activity
// @route   POST /api/tasks/:taskId/activities/attachment
// @access  Private
const addAttachment = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { attachments } = req.body;

    if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
      return res.status(400).json({ message: "At least one attachment is required" });
    }

    // Verify user has access to this task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    const isInvolved = task.createdBy.toString() === req.user._id.toString() ||
                      task.assignedTo.some(userId => userId.toString() === req.user._id.toString());
    
    if (!isInvolved) {
      return res.status(403).json({ message: "Access denied to this task" });
    }

    // Validate attachment format
    const validAttachments = attachments.filter(att => att.name && att.url);
    if (validAttachments.length === 0) {
      return res.status(400).json({ message: "Invalid attachment format" });
    }

    // Create attachment activity
    const activity = await Activity.createAttachmentActivity(taskId, req.user._id, validAttachments);
    
    // Populate user data
    await activity.populate('user', 'name email profileImageUrl');

    res.status(201).json({
      message: "Attachment(s) added successfully",
      activity
    });
  } catch (error) {
    console.error("Error adding attachment:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Update/edit a comment
// @route   PUT /api/tasks/:taskId/activities/:activityId
// @access  Private
const updateActivity = async (req, res) => {
  try {
    const { taskId, activityId } = req.params;
    const { text } = req.body;

    // Find the activity
    const activity = await Activity.findById(activityId);
    if (!activity) {
      return res.status(404).json({ message: "Activity not found" });
    }

    // Verify it belongs to the task
    if (activity.task.toString() !== taskId) {
      return res.status(400).json({ message: "Activity does not belong to this task" });
    }

    // Verify user owns this activity
    if (activity.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: "You can only edit your own activities" });
    }

    // Only comments can be edited
    if (activity.type !== 'COMMENT') {
      return res.status(400).json({ message: "Only comments can be edited" });
    }

    if (!text || text.trim().length === 0) {
      return res.status(400).json({ message: "Comment text is required" });
    }

    // Update the activity
    activity.content.text = text.trim();
    activity.isEdited = true;
    activity.editedAt = new Date();
    
    await activity.save();
    await activity.populate('user', 'name email profileImageUrl');

    res.json({
      message: "Comment updated successfully",
      activity
    });
  } catch (error) {
    console.error("Error updating activity:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Delete an activity
// @route   DELETE /api/tasks/:taskId/activities/:activityId
// @access  Private
const deleteActivity = async (req, res) => {
  try {
    const { taskId, activityId } = req.params;

    // Find the activity
    const activity = await Activity.findById(activityId);
    if (!activity) {
      return res.status(404).json({ message: "Activity not found" });
    }

    // Verify it belongs to the task
    if (activity.task.toString() !== taskId) {
      return res.status(400).json({ message: "Activity does not belong to this task" });
    }

    // Verify user owns this activity or is task creator
    const task = await Task.findById(taskId);
    const canDelete = activity.user.toString() === req.user._id.toString() ||
                     task.createdBy.toString() === req.user._id.toString();
    
    if (!canDelete) {
      return res.status(403).json({ message: "You can only delete your own activities or if you're the task creator" });
    }

    // Soft delete - just hide the activity
    activity.isVisible = false;
    await activity.save();

    res.json({ message: "Activity deleted successfully" });
  } catch (error) {
    console.error("Error deleting activity:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get activity statistics for collaboration ranking
// @route   GET /api/tasks/:taskId/activities/stats
// @access  Private
const getActivityStats = async (req, res) => {
  try {
    const { taskId } = req.params;

    // Verify user has access to this task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    const isInvolved = task.createdBy.toString() === req.user._id.toString() ||
                      task.assignedTo.some(userId => userId.toString() === req.user._id.toString());
    
    if (!isInvolved) {
      return res.status(403).json({ message: "Access denied to this task" });
    }

    // Get activity statistics
    const stats = await Activity.aggregate([
      { $match: { task: task._id, isVisible: true } },
      {
        $group: {
          _id: '$user',
          totalActivities: { $sum: 1 },
          comments: { $sum: { $cond: [{ $eq: ['$type', 'COMMENT'] }, 1, 0] } },
          attachments: { $sum: { $cond: [{ $eq: ['$type', 'ATTACHMENT_ADDED'] }, 1, 0] } },
          avgResponseTime: { $avg: '$metrics.responseTime' },
          totalEngagementScore: { $sum: '$metrics.engagementScore' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          user: {
            _id: '$user._id',
            name: '$user.name',
            email: '$user.email',
            profileImageUrl: '$user.profileImageUrl'
          },
          totalActivities: 1,
          comments: 1,
          attachments: 1,
          avgResponseTime: { $round: ['$avgResponseTime', 2] },
          totalEngagementScore: 1
        }
      },
      { $sort: { totalEngagementScore: -1 } }
    ]);

    res.json({ stats });
  } catch (error) {
    console.error("Error fetching activity stats:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

export {
  getTaskActivities,
  addComment,
  addAttachment,
  updateActivity,
  deleteActivity,
  getActivityStats
};
