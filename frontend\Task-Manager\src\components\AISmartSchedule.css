/* Ultra Modern AI Smart Schedule */
.ai-schedule-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  color: #1e293b;
  padding: 0;
  position: relative;
  overflow-x: hidden;
}

.ai-schedule-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  z-index: 0;
}

.ai-schedule-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  padding: 32px 32px 32px 32px;
  text-align: center;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-top {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.back-button:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.back-button:active {
  transform: translateY(0);
}

.ai-schedule-header h1 {
  margin: 0 0 24px 0;
  color: #0f172a;
  font-size: 36px;
  font-weight: 700;
  letter-spacing: -0.5px;
  line-height: 1.2;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-schedule-header p {
  margin: 0 0 24px 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.schedule-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #475569;
  font-weight: 600;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.legend-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(148, 163, 184, 0.3);
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  flex-shrink: 0;
}

.legend-color.work {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.legend-color.attachment {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.legend-color.break {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Ultra Modern Calendar Wrapper */
.calendar-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-radius: 20px;
  margin: 32px;
  padding: 0;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 20px 40px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.1);
  overflow: hidden;
  position: relative;
  z-index: 1;
  width: calc(100% - 64px);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Modern Calendar Toolbar */
.ai-calendar-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.toolbar-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 80px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.nav-button:active {
  transform: translateY(0);
}

.calendar-title {
  margin: 0;
  color: #1a1a1a;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.toolbar-views {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.view-button {
  background: transparent;
  color: #374151;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 60px;
  height: 36px;
  position: relative;
  overflow: hidden;
}

.view-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.view-button.active {
  background: rgba(255, 255, 255, 0.9);
  color: #1a1a1a;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Modern Beautiful Calendar */
.rbc-calendar {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: transparent;
  padding: 0 24px 24px 24px;
}

.rbc-header {
  background: #f8fafc;
  color: #64748b;
  font-weight: 700;
  font-size: 13px;
  padding: 20px 16px;
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.rbc-today {
  background: #dbeafe !important;
  color: #1e40af !important;
  font-weight: 700;
  position: relative;
}

.rbc-today::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: #3b82f6;
  border-radius: 8px;
  opacity: 0.1;
  pointer-events: none;
}

.rbc-event {
  border-radius: 8px !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  padding: 6px 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.rbc-event:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2) !important;
}

.rbc-event:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Apple-Style Event Colors */
.rbc-event.work-session {
  background: #34c759 !important;
  color: white !important;
}

.rbc-event.attachment-processing {
  background: #ff9500 !important;
  color: white !important;
}

.rbc-event.break-time {
  background: #af52de !important;
  color: white !important;
}

/* Apple-Style Loading */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 20px;
  background: #ffffff;
  border-radius: 16px;
  margin: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border: 0.33px solid rgba(60, 60, 67, 0.18);
}

.spinner {
  width: 44px;
  height: 44px;
  border: 4px solid #f2f2f7;
  border-top: 4px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #86868b;
  font-size: 21px;
  font-weight: 400;
  text-align: center;
  line-height: 1.381;
}

/* Apple-Style Modal */
.task-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
}

.modal-content {
  background: #ffffff;
  border-radius: 20px;
  max-width: 600px;
  width: 92%;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  border: 0.33px solid rgba(60, 60, 67, 0.18);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px 20px 32px;
  border-bottom: 0.33px solid rgba(60, 60, 67, 0.29);
  background: #ffffff;
}

.modal-header h3 {
  margin: 0;
  color: #1d1d1f;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.003em;
}

.close-button {
  background: #f2f2f7;
  border: none;
  font-size: 20px;
  color: #86868b;
  cursor: pointer;
  padding: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.15s ease;
  border: 0.33px solid rgba(60, 60, 67, 0.18);
}

.close-button:hover {
  background: #e5e5ea;
  color: #1d1d1f;
}

.close-button:active {
  background: #d1d1d6;
  transform: scale(0.94);
}

.modal-body {
  padding: 32px;
  overflow-y: auto;
  max-height: calc(85vh - 140px);
}

.work-details, .attachment-details {
  line-height: 1.381;
}

.work-details p, .attachment-details p {
  margin: 0 0 16px 0;
  color: #1d1d1f;
  font-size: 17px;
  font-weight: 400;
}

.work-details strong, .attachment-details strong {
  color: #1d1d1f;
  font-weight: 600;
}

.ai-insights {
  background: #f9f9f9;
  color: #1d1d1f;
  padding: 24px;
  border-radius: 16px;
  margin-top: 24px;
  border: 0.33px solid rgba(60, 60, 67, 0.18);
}

.ai-insights h4 {
  margin: 0 0 12px 0;
  font-size: 19px;
  font-weight: 700;
  color: #1d1d1f;
  letter-spacing: -0.003em;
}

.ai-insights p {
  margin: 0 0 12px 0;
  line-height: 1.381;
  font-size: 17px;
  color: #1d1d1f;
}

.requirements {
  background: #f9f9f9;
  padding: 24px;
  border-radius: 16px;
  margin-top: 24px;
  border: 0.33px solid rgba(60, 60, 67, 0.18);
}

.requirements h4 {
  margin: 0 0 16px 0;
  color: #1d1d1f;
  font-size: 19px;
  font-weight: 700;
  letter-spacing: -0.003em;
}

.requirements ul {
  margin: 0;
  padding-left: 24px;
}

.requirements li {
  margin-bottom: 12px;
  color: #1d1d1f;
  line-height: 1.381;
  font-size: 17px;
}

/* Apple-Style Responsive Design */
@media (max-width: 768px) {
  .ai-schedule-header {
    padding: 40px 16px 32px 16px;
  }

  .ai-schedule-header h1 {
    font-size: 36px;
  }

  .ai-schedule-header p {
    font-size: 19px;
  }

  .schedule-legend {
    gap: 12px;
  }

  .legend-item {
    font-size: 15px;
    padding: 10px 14px;
  }

  .calendar-wrapper {
    margin: 16px;
  }

  .ai-calendar-toolbar {
    flex-direction: column;
    gap: 20px;
    padding: 20px 24px;
  }

  .toolbar-navigation {
    justify-content: center;
  }

  .calendar-title {
    font-size: 24px;
  }

  .nav-button {
    font-size: 15px;
    min-width: 80px;
    height: 40px;
  }

  .view-button {
    font-size: 15px;
    min-width: 64px;
    height: 32px;
  }

  .modal-content {
    width: 94%;
    margin: 16px;
    border-radius: 16px;
  }

  .modal-header {
    padding: 24px 24px 16px 24px;
  }

  .modal-header h3 {
    font-size: 21px;
  }

  .modal-body {
    padding: 24px;
  }

  .rbc-calendar {
    padding: 0 20px 20px 20px;
  }

  .rbc-header {
    font-size: 15px;
    padding: 12px 8px;
  }
}

/* Modern Calendar Grid */
.rbc-date-cell {
  padding: 12px 16px !important;
  text-align: right;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  position: relative;
  transition: all 0.2s ease;
  height: 90px !important;
  min-height: 90px !important;
  max-height: 90px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: flex-end !important;
  box-sizing: border-box !important;
  border-right: 1px solid #f1f5f9 !important;
  background: #ffffff;
}

.rbc-date-cell:last-child {
  border-right: none !important;
}

.rbc-date-cell:hover {
  background: #f8fafc !important;
  color: #3b82f6 !important;
}

.rbc-date-cell:hover {
  background: rgba(255, 255, 255, 0.1);
}

.rbc-off-range-bg {
  background: rgba(0, 0, 0, 0.02);
  opacity: 0.5;
}

.rbc-current-time-indicator {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 3px;
  z-index: 3;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Modern Calendar Structure */
.rbc-calendar {
  border: none !important;
  border-radius: 20px;
  overflow: hidden;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative !important;
  width: 100% !important;
  height: 600px !important;
}

.rbc-month-view {
  border: none;
  border-radius: 0;
  background: #ffffff;
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
}

/* Stable Layout for Equal Heights */
.rbc-month-view .rbc-month-body {
  height: 540px !important;
  background: #ffffff;
  display: flex !important;
  flex-direction: column !important;
}

.rbc-month-view .rbc-row {
  flex: 1 !important;
  height: 90px !important;
  min-height: 90px !important;
  max-height: 90px !important;
  border-bottom: 1px solid #f1f5f9 !important;
  display: flex !important;
}

.rbc-month-view .rbc-row:last-child {
  border-bottom: none !important;
}

.rbc-month-view .rbc-row .rbc-row-content {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
}

.rbc-month-view .rbc-row .rbc-row-segment {
  height: 100% !important;
  flex: 1 !important;
}

.rbc-month-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  height: 80px !important;
  min-height: 80px !important;
  max-height: 80px !important;
  transition: all 0.3s ease;
  display: flex !important;
  align-items: stretch !important;
}

.rbc-month-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.rbc-month-row:last-child {
  border-bottom: none;
  height: 80px !important;
  min-height: 80px !important;
  max-height: 80px !important;
}

.rbc-day-bg {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.rbc-day-bg:hover {
  background: rgba(255, 255, 255, 0.05);
}

.rbc-day-bg:last-child {
  border-right: none;
}

.rbc-time-view {
  border: none;
}

.rbc-time-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.rbc-time-content {
  border-top: none;
}

.rbc-timeslot-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.rbc-time-slot {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Calendar Date Numbers */
.rbc-date-cell a {
  color: #374151;
  text-decoration: none;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: inline-block;
  min-width: 32px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.rbc-date-cell a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.rbc-date-cell a:hover::before {
  opacity: 1;
}

.rbc-date-cell a:hover {
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rbc-today .rbc-date-cell a {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  transform: scale(1.1);
}

.rbc-today .rbc-date-cell a:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.5);
}

/* Modern Beautiful Scrollbars */
.modal-body::-webkit-scrollbar,
.ai-schedule-container::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track,
.ai-schedule-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb,
.ai-schedule-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.ai-schedule-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

/* Modern Calendar Week View */
.rbc-time-gutter {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.rbc-time-header-gutter {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Calendar All Day Events */
.rbc-allday-cell {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Beautiful Event Colors */
.rbc-event.work-session {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.rbc-event.attachment-processing {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.rbc-event.recommended-break {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

/* Beautiful Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-wrapper {
  animation: float 6s ease-in-out infinite, fadeInUp 0.8s ease-out;
}

.ai-schedule-header {
  animation: fadeInUp 0.6s ease-out;
}

.schedule-legend {
  animation: fadeInUp 1s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-schedule-header h1 {
    font-size: 28px;
  }

  .ai-schedule-header p {
    font-size: 16px;
  }

  .calendar-wrapper {
    margin: 16px 8px;
    border-radius: 16px;
  }

  .ai-calendar-toolbar {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
  }

  .schedule-legend {
    gap: 12px;
  }

  .legend-item {
    padding: 8px 16px;
    font-size: 12px;
  }

  .nav-button {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  .ai-schedule-header {
    padding: 40px 16px 24px 16px;
  }

  .calendar-wrapper {
    margin: 12px 4px;
  }

  .rbc-calendar {
    padding: 0 12px 12px 12px;
  }
}
