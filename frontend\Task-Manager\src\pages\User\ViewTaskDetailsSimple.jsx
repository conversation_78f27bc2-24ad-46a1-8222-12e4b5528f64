import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import AvatarGroup from "../../components/AvatarGroup";
import ActivitiesTimeline from "../../components/Activities/ActivitiesTimeline";
import moment from "moment";
import { LuSquareArrowOutUpRight, LuDownload, LuEye, LuArrowLeft, LuTrendingUp, LuUsers, LuClipboardList, LuMessageCircle } from "react-icons/lu";
import { toast } from "react-hot-toast";
import FileUploadService from "../../services/fileUploadService";
import { useNotifications } from "../../contexts/NotificationContext";

const ViewTaskDetailsSimple = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [activeTab, setActiveTab] = useState('details'); // 'details' or 'activities'

  const [currentUser, setCurrentUser] = useState(null);
  const { refreshNotifications } = useNotifications();

  useEffect(() => {
    fetchTask();
    fetchCurrentUser();
  }, [id]);

  const fetchCurrentUser = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.AUTH.GET_PROFILE);
      setCurrentUser(response.data.user);
    } catch (error) {
      console.error("Error fetching current user:", error);
    }
  };

  const fetchTask = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_TASK_BY_ID(id));
      setTask(response.data.task);
    } catch (error) {
      console.error("Error fetching task:", error);
      toast.error("Failed to load task details");
    }
  };

  const handleEdit = () => {
    navigate(`/user/edit-task/${id}`);
  };

  const handleDelete = async () => {
    if (window.confirm("Are you sure you want to delete this task?")) {
      try {
        await axiosInstance.delete(API_PATHS.TASKS.DELETE_TASK(id));
        toast.success("Task deleted successfully");
        navigate("/user/my-tasks");
      } catch (error) {
        console.error("Error deleting task:", error);
        toast.error("Failed to delete task");
      }
    }
  };

  const getStatusTagColor = (status) => {
    switch (status) {
      case "Pending":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "text-red-600";
      case "Medium":
        return "text-yellow-600";
      case "Low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  if (!task) {
    return (
      <DashboardLayout activeMenu="My Tasks">
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeMenu="My Tasks">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Apple-style Navigation */}
        <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
          <div className="max-w-4xl mx-auto px-6 py-4">
            <button
              onClick={() => navigate("/user/my-tasks")}
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors"
            >
              <LuArrowLeft className="w-4 h-4" />
              Back to My Tasks
            </button>
          </div>
        </div>

        {task && (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 overflow-hidden">
              {/* Apple-style Header */}
              <div className="p-8 pb-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <h1 className="text-3xl font-semibold text-gray-900 leading-tight mb-3">
                      {task?.title}
                    </h1>
                    <div className="flex items-center gap-3">
                      <div
                        className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${getStatusTagColor(
                          task?.status
                        )}`}
                      >
                        {task?.status}
                      </div>
                      <span className="text-gray-400">•</span>
                      <span className="text-sm text-gray-600">
                        Due {moment(task?.dueDate).format("MMM D, YYYY")}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 ml-6">
                    <button
                      onClick={handleEdit}
                      className="inline-flex items-center gap-2 px-4 py-2.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-xl transition-all duration-300 ease-out shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105"
                    >
                      ✏️ Edit
                    </button>

                    <button
                      onClick={handleDelete}
                      className="inline-flex items-center gap-2 px-4 py-2.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-xl transition-all duration-300 ease-out shadow-lg shadow-red-500/25 hover:shadow-red-500/40 hover:scale-105"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>

              {/* Tab Navigation - matching your reference design */}
              <div className="border-b border-gray-200/50 px-8">
                <div className="flex space-x-8">
                  <button
                    onClick={() => setActiveTab('details')}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'details'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <LuClipboardList className="w-4 h-4" />
                    Task Details
                  </button>
                  <button
                    onClick={() => setActiveTab('activities')}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'activities'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <LuMessageCircle className="w-4 h-4" />
                    Activities/Timeline
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              {activeTab === 'details' ? (
                <div className="px-8 space-y-6 py-6">
                  {/* Description Card */}
                  <div className="bg-gray-50/50 rounded-2xl p-6 border border-gray-100/50">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                    <p className="text-gray-700 leading-relaxed">{task?.description}</p>
                  </div>

                  {/* Task Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Priority & Status */}
                    <div className="bg-gray-50/50 rounded-2xl p-6 border border-gray-100/50">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Priority & Status</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Priority:</span>
                          <span className={`font-medium ${getPriorityColor(task?.priority)}`}>
                            {task?.priority}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Status:</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusTagColor(task?.status)}`}>
                            {task?.status}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Assigned Users */}
                    <div className="bg-gray-50/50 rounded-2xl p-6 border border-gray-100/50">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Assigned To</h3>
                      {task?.assignedTo && task.assignedTo.length > 0 ? (
                        <AvatarGroup users={task.assignedTo} maxDisplay={3} />
                      ) : (
                        <p className="text-gray-500">No users assigned</p>
                      )}
                    </div>
                  </div>

                  {/* Attachments */}
                  {task?.attachments && task.attachments.length > 0 && (
                    <div className="bg-gray-50/50 rounded-2xl p-6 border border-gray-100/50">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Attachments</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {task.attachments.map((attachment, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-3 p-3 bg-white rounded-xl border border-gray-200 hover:shadow-md transition-shadow"
                          >
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <LuSquareArrowOutUpRight className="w-5 h-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {attachment.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {attachment.size ? `${(attachment.size / 1024).toFixed(1)} KB` : 'Unknown size'}
                              </p>
                            </div>
                            <div className="flex items-center gap-1">
                              <button
                                onClick={() => setFilePreview(attachment)}
                                className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                              >
                                <LuEye className="w-4 h-4" />
                              </button>
                              <a
                                href={attachment.url}
                                download={attachment.name}
                                className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                              >
                                <LuDownload className="w-4 h-4" />
                              </a>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* Activities/Timeline Tab */
                <div className="px-8 py-6">
                  <ActivitiesTimeline 
                    taskId={id} 
                    currentUser={currentUser}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ViewTaskDetailsSimple;
