export const BASE_URL = "http://localhost:8000";

export const API_PATHS = {
  AUTH: {
    REGISTER: "/api/auth/register",
    LOGIN: "/api/auth/login",
    GET_PROFILE: "/api/auth/profile",
  },

  USERS: {
    GET_ALL_USERS: "/api/users",
    GET_USER_BY_ID: (userId) => `/api/users/${userId}`,
    GET_USERS_BY_TEAM: (teamId) => `/api/users/team/${teamId}`, // ✅ Add this
    GET_FRIENDS: "/api/users/friends",
    CREATE_USER: "/api/users",
    UPDATE_USER: (userId) => `/api/users/${userId}`,
    DELETE_USER: (userId) => `/api/users/${userId}`,
  },

  TASKS: {
    GET_DASHBOARD_DATA: "/api/tasks/dashboard-data",
    GET_USER_DASHBOARD_DATA: "/api/tasks/user-dashboard-data",
    GET_ALL_TASKS: "/api/tasks",
    GET_TASK_BY_ID: (taskId) => `/api/tasks/${taskId}`,
    CREATE_TASK: "/api/tasks",
    UPDATE_TASK: (taskId) => `/api/tasks/${taskId}`,
    DELETE_TASK: (taskId) => `/api/tasks/${taskId}`,
    UPDATE_TASK_STATUS: (taskId) => `/api/tasks/${taskId}/status`,
    UPDATE_TODO_CHECKLIST: (taskId) => `/api/tasks/${taskId}/todo`,

    // Activity endpoints
    GET_ACTIVITIES: (taskId) => `/api/tasks/${taskId}/activities`,
    ADD_COMMENT: (taskId) => `/api/tasks/${taskId}/activities/comment`,
    ADD_ATTACHMENT: (taskId) => `/api/tasks/${taskId}/activities/attachment`,
    UPDATE_ACTIVITY: (taskId, activityId) => `/api/tasks/${taskId}/activities/${activityId}`,
    DELETE_ACTIVITY: (taskId, activityId) => `/api/tasks/${taskId}/activities/${activityId}`,
    GET_ACTIVITY_STATS: (taskId) => `/api/tasks/${taskId}/activities/stats`,
  },

  REPORTS: {
    EXPORT_TASKS: "/api/reports/export/tasks",
    EXPORT_USERS: "/api/reports/export/users",
  },

  IMAGE: {
    UPLOAD_IMAGE: "api/auth/upload-image",
  },

  
  AI: {
    PRIORITIZE: "/api/ai/generate-prioritization",
    EXPLAIN: "/api/ai/generate-explanation",
  },

  NOTIFICATIONS: {
    BASE: "/api/notifications",
    GET_NOTIFICATIONS: "/api/notifications",
    GET_NOTIFICATION_BY_ID: (notificationId) => `/api/notifications/${notificationId}`,
    CREATE_NOTIFICATION: "/api/notifications",
    MARK_AS_READ: (notificationId) => `/api/notifications/${notificationId}/read`,
    MARK_AS_UNREAD: (notificationId) => `/api/notifications/${notificationId}/unread`,
    MARK_ALL_READ: "/api/notifications/mark-all-read",
    DELETE_NOTIFICATION: (notificationId) => `/api/notifications/${notificationId}`,
    CLEAR_READ_NOTIFICATIONS: "/api/notifications/clear-read",
    GET_NOTIFICATION_STATS: "/api/notifications/stats",
    EMAIL_VERIFY: "/api/notifications/email/verify",
    EMAIL_TEST: "/api/notifications/email/test",
  },

};

