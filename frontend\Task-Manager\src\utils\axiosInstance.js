import axios from "axios";
import { BASE_URL } from "./apiPaths";

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // Increased to 30 seconds for AI operations
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request Interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = localStorage.getItem("token");
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response Interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors globally
    if (error.response) {
      if (error.response.status === 401) {
        // Redirect to login page
        localStorage.removeItem("token");
        window.location.href = "/login";
      } else if (error.response.status === 500) {
        console.error("Server error. Please try again later.");
      } else if (error.response.status === 503) {
        console.error("Service temporarily unavailable. Please try again later.");
      }
    } else if (error.code === "ECONNABORTED") {
      console.error("Request timeout. The AI service may be processing your request. Please try again in a moment.");
    } else if (error.code === "ECONNREFUSED") {
      console.error("Unable to connect to server. Please check your connection.");
    } else if (error.code === "NETWORK_ERROR") {
      console.error("Network error. Please check your internet connection.");
    }
    return Promise.reject(error);
  }
);

// Create a specialized instance for AI operations with longer timeout
export const aiAxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 60000, // 60 seconds for AI operations
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Apply the same interceptors to AI instance
aiAxiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = localStorage.getItem("token");
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

aiAxiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      if (error.response.status === 401) {
        localStorage.removeItem("token");
        window.location.href = "/login";
      } else if (error.response.status === 500) {
        console.error("AI service error. Please try again later.");
      } else if (error.response.status === 503) {
        console.error("AI service temporarily unavailable. Please try again later.");
      }
    } else if (error.code === "ECONNABORTED") {
      console.error("AI request timeout. The AI service is taking longer than expected. Please try again.");
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
