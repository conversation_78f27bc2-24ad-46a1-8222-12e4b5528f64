import express from "express";
import { protect } from "../middlewares/authMiddleware.js";
import {
  getTaskActivities,
  addComment,
  addAttachment,
  updateActivity,
  deleteActivity,
  getActivityStats
} from "../controllers/activityController.js";

const router = express.Router();

// All routes are nested under /api/tasks/:taskId/activities
// and require authentication

// @route   GET /api/tasks/:taskId/activities
// @desc    Get all activities for a specific task
// @access  Private
router.get("/:taskId/activities", protect, getTaskActivities);

// @route   POST /api/tasks/:taskId/activities/comment
// @desc    Add a comment to a task
// @access  Private
router.post("/:taskId/activities/comment", protect, addComment);

// @route   POST /api/tasks/:taskId/activities/attachment
// @desc    Add attachment(s) to a task activity
// @access  Private
router.post("/:taskId/activities/attachment", protect, addAttachment);

// @route   PUT /api/tasks/:taskId/activities/:activityId
// @desc    Update/edit an activity (comments only)
// @access  Private
router.put("/:taskId/activities/:activityId", protect, updateActivity);

// @route   DELETE /api/tasks/:taskId/activities/:activityId
// @desc    Delete an activity (soft delete)
// @access  Private
router.delete("/:taskId/activities/:activityId", protect, deleteActivity);

// @route   GET /api/tasks/:taskId/activities/stats
// @desc    Get activity statistics for collaboration ranking
// @access  Private
router.get("/:taskId/activities/stats", protect, getActivityStats);

export default router;
