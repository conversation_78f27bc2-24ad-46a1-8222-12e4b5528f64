import React, { useState, useEffect } from 'react';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import moment from 'moment';
import { LuUser, LuMessageCircle, LuPaperclip, LuEdit3, LuTrash2, LuDownload, <PERSON>Eye } from 'react-icons/lu';
import { toast } from 'react-hot-toast';

const ActivitiesTimeline = ({ taskId, currentUser }) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [newAttachments, setNewAttachments] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [editingActivity, setEditingActivity] = useState(null);
  const [editText, setEditText] = useState('');

  useEffect(() => {
    if (taskId) {
      fetchActivities();
    }
  }, [taskId]);

  const fetchActivities = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_ACTIVITIES(taskId));
      setActivities(response.data.activities || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
      toast.error('Failed to load activities');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    try {
      setSubmitting(true);
      const response = await axiosInstance.post(API_PATHS.TASKS.ADD_COMMENT(taskId), {
        text: newComment.trim()
      });

      setActivities(prev => [response.data.activity, ...prev]);
      setNewComment('');
      toast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddAttachment = async (files) => {
    if (!files || files.length === 0) return;

    try {
      setSubmitting(true);
      
      // Convert files to attachment format (you'll need to implement file upload)
      const attachments = Array.from(files).map(file => ({
        name: file.name,
        url: URL.createObjectURL(file), // Temporary - replace with actual upload
        size: file.size,
        type: file.type
      }));

      const response = await axiosInstance.post(API_PATHS.TASKS.ADD_ATTACHMENT(taskId), {
        attachments
      });

      setActivities(prev => [response.data.activity, ...prev]);
      toast.success('Attachment(s) added successfully');
    } catch (error) {
      console.error('Error adding attachment:', error);
      toast.error('Failed to add attachment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditComment = async (activityId) => {
    if (!editText.trim()) return;

    try {
      const response = await axiosInstance.put(API_PATHS.TASKS.UPDATE_ACTIVITY(taskId, activityId), {
        text: editText.trim()
      });

      setActivities(prev => prev.map(activity => 
        activity._id === activityId ? response.data.activity : activity
      ));
      
      setEditingActivity(null);
      setEditText('');
      toast.success('Comment updated successfully');
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    }
  };

  const handleDeleteActivity = async (activityId) => {
    if (!window.confirm('Are you sure you want to delete this activity?')) return;

    try {
      await axiosInstance.delete(API_PATHS.TASKS.DELETE_ACTIVITY(taskId, activityId));
      setActivities(prev => prev.filter(activity => activity._id !== activityId));
      toast.success('Activity deleted successfully');
    } catch (error) {
      console.error('Error deleting activity:', error);
      toast.error('Failed to delete activity');
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'COMMENT':
        return <LuMessageCircle className="w-4 h-4" />;
      case 'ATTACHMENT_ADDED':
        return <LuPaperclip className="w-4 h-4" />;
      case 'STATUS_CHANGED':
        return <div className="w-4 h-4 bg-blue-500 rounded-full"></div>;
      case 'PRIORITY_CHANGED':
        return <div className="w-4 h-4 bg-orange-500 rounded-full"></div>;
      case 'ASSIGNMENT_CHANGED':
        return <LuUser className="w-4 h-4" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded-full"></div>;
    }
  };

  const getActivityTypeLabel = (type) => {
    switch (type) {
      case 'COMMENT': return 'Commented';
      case 'ATTACHMENT_ADDED': return 'Added attachment';
      case 'STATUS_CHANGED': return 'Changed status';
      case 'PRIORITY_CHANGED': return 'Changed priority';
      case 'ASSIGNMENT_CHANGED': return 'Updated assignments';
      case 'TASK_CREATED': return 'Created task';
      default: return 'Updated';
    }
  };

  const filteredActivities = activities.filter(activity => {
    if (activeTab === 'all') return true;
    if (activeTab === 'comments') return activity.type === 'COMMENT';
    if (activeTab === 'attachments') return activity.type === 'ATTACHMENT_ADDED';
    if (activeTab === 'system') return !['COMMENT', 'ATTACHMENT_ADDED'].includes(activity.type);
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading activities...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header with tabs - matching your reference design */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Activities</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setActiveTab('all')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                activeTab === 'all' 
                  ? 'bg-blue-100 text-blue-700 font-medium' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setActiveTab('comments')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                activeTab === 'comments' 
                  ? 'bg-blue-100 text-blue-700 font-medium' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Comments
            </button>
            <button
              onClick={() => setActiveTab('attachments')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                activeTab === 'attachments' 
                  ? 'bg-blue-100 text-blue-700 font-medium' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Attachments
            </button>
          </div>
        </div>
      </div>

      {/* Add Activity Form - matching your reference design */}
      <div className="border-b border-gray-200 px-6 py-4 bg-gray-50">
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Add Activity</h4>
          
          {/* Activity Type Checkboxes - matching your reference */}
          <div className="flex items-center gap-6 mb-4">
            <label className="flex items-center gap-2">
              <input type="checkbox" defaultChecked className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">Started</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">Completed</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">In Progress</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">Commented</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600" />
              <span className="text-sm text-gray-700">Bug</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">Assigned</span>
            </label>
          </div>
        </div>

        {/* Comment Form */}
        <form onSubmit={handleAddComment} className="space-y-3">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Type..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
          />
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <input
                type="file"
                multiple
                onChange={(e) => handleAddAttachment(e.target.files)}
                className="hidden"
                id="attachment-upload"
              />
              <label
                htmlFor="attachment-upload"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 cursor-pointer"
              >
                <LuPaperclip className="w-4 h-4" />
                Attach files
              </label>
            </div>
            
            <button
              type="submit"
              disabled={submitting || !newComment.trim()}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </form>
      </div>

      {/* Activities Timeline */}
      <div className="px-6 py-4">
        {filteredActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <LuMessageCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>No activities yet</p>
            <p className="text-sm">Be the first to add a comment or attachment!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredActivities.map((activity) => (
              <ActivityItem
                key={activity._id}
                activity={activity}
                currentUser={currentUser}
                onEdit={(activityId, text) => {
                  setEditingActivity(activityId);
                  setEditText(text);
                }}
                onSaveEdit={handleEditComment}
                onCancelEdit={() => {
                  setEditingActivity(null);
                  setEditText('');
                }}
                onDelete={handleDeleteActivity}
                isEditing={editingActivity === activity._id}
                editText={editText}
                setEditText={setEditText}
                getActivityIcon={getActivityIcon}
                getActivityTypeLabel={getActivityTypeLabel}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Individual Activity Item Component - matching your reference design
const ActivityItem = ({
  activity,
  currentUser,
  onEdit,
  onSaveEdit,
  onCancelEdit,
  onDelete,
  isEditing,
  editText,
  setEditText,
  getActivityIcon,
  getActivityTypeLabel
}) => {
  const isOwner = activity.user._id === currentUser?._id;
  const canEdit = isOwner && activity.type === 'COMMENT';
  const canDelete = isOwner;

  return (
    <div className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      {/* User Avatar - matching your reference */}
      <div className="flex-shrink-0">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
          {activity.user.name?.charAt(0)?.toUpperCase() || 'U'}
        </div>
      </div>

      {/* Activity Content */}
      <div className="flex-1 min-w-0">
        {/* Activity Header */}
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium text-gray-900 text-sm">
            {activity.user.name || 'Unknown User'}
          </span>
          <span className="text-gray-500 text-sm">
            {getActivityTypeLabel(activity.type)}
          </span>
          <span className="text-gray-400 text-xs">
            {moment(activity.createdAt).fromNow()}
          </span>
          {activity.isEdited && (
            <span className="text-gray-400 text-xs">(edited)</span>
          )}
        </div>

        {/* Activity Content */}
        <div className="text-sm text-gray-700">
          {activity.type === 'COMMENT' ? (
            isEditing ? (
              <div className="space-y-2">
                <textarea
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={2}
                />
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => onSaveEdit(activity._id)}
                    className="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700"
                  >
                    Save
                  </button>
                  <button
                    onClick={onCancelEdit}
                    className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <p>{activity.content.text}</p>
            )
          ) : activity.type === 'ATTACHMENT_ADDED' ? (
            <div className="space-y-2">
              {activity.content.attachments?.map((attachment, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-100 rounded-md">
                  <LuPaperclip className="w-4 h-4 text-gray-500" />
                  <span className="flex-1 truncate">{attachment.name}</span>
                  <div className="flex items-center gap-1">
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <LuEye className="w-3 h-3" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <LuDownload className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600">
              {activity.content.changes ?
                `${activity.content.changes.field}: "${activity.content.changes.oldValue}" → "${activity.content.changes.newValue}"` :
                'System activity'
              }
            </p>
          )}
        </div>

        {/* Activity Actions */}
        {(canEdit || canDelete) && !isEditing && (
          <div className="flex items-center gap-2 mt-2">
            {canEdit && (
              <button
                onClick={() => onEdit(activity._id, activity.content.text)}
                className="inline-flex items-center gap-1 px-2 py-1 text-xs text-gray-500 hover:text-gray-700"
              >
                <LuEdit3 className="w-3 h-3" />
                Edit
              </button>
            )}
            {canDelete && (
              <button
                onClick={() => onDelete(activity._id)}
                className="inline-flex items-center gap-1 px-2 py-1 text-xs text-red-500 hover:text-red-700"
              >
                <LuTrash2 className="w-3 h-3" />
                Delete
              </button>
            )}
          </div>
        )}
      </div>

      {/* Activity Type Icon */}
      <div className="flex-shrink-0 mt-1">
        <div className="w-6 h-6 flex items-center justify-center text-gray-400">
          {getActivityIcon(activity.type)}
        </div>
      </div>
    </div>
  );
};

export default ActivitiesTimeline;
